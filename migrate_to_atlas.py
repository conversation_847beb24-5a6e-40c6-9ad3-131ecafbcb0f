#!/usr/bin/env python3
"""
MongoDB Data Migration Script: Local to Atlas
==============================================

This script migrates all data from a local MongoDB database to MongoDB Atlas,
preserving data integrity and relationships while handling conflicts gracefully.

Features:
- Connects to both local MongoDB and MongoDB Atlas
- Migrates all collections with proper error handling
- Preserves ObjectId references and relationships
- Idempotent operation (safe to run multiple times)
- Comprehensive verification and logging
- Handles duplicate data conflicts
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, ConnectionFailure
from bson import ObjectId
import json
from collections import defaultdict
import traceback

# Add src directory to path for config access
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class MongoMigrator:
    def __init__(self):
        """Initialize the MongoDB migrator with connection details."""
        self.local_uri = "mongodb://localhost:27017/"
        self.local_db_name = "sisarasa"
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        
        # Migration statistics
        self.stats = defaultdict(lambda: {
            'total': 0,
            'migrated': 0,
            'skipped': 0,
            'errors': 0
        })
        
        # Collections to migrate (in order to preserve relationships)
        self.collections_to_migrate = [
            'users',
            'recipes', 
            'reviews',
            'community_posts',
            'community_recipes',
            'verifications',
            'post_comments',
            'recipe_ratings'
        ]
        
        self.local_client = None
        self.atlas_client = None
        self.local_db = None
        self.atlas_db = None

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string from environment or config."""
        try:
            # Try to load from .env file
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            # Fallback to environment variable
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found in .env file or environment variables")
                
            print(f"✅ Atlas URI loaded: {self.atlas_uri.replace(self.atlas_uri.split('@')[0].split('//')[1] + '@', '***:***@') if '@' in self.atlas_uri else self.atlas_uri}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_databases(self):
        """Establish connections to both local and Atlas databases."""
        try:
            print("🔗 Connecting to databases...")
            
            # Connect to local MongoDB
            print(f"📍 Connecting to local MongoDB: {self.local_uri}")
            self.local_client = MongoClient(self.local_uri, serverSelectionTimeoutMS=5000)
            self.local_db = self.local_client[self.local_db_name]
            
            # Test local connection
            self.local_client.admin.command('ping')
            print("✅ Local MongoDB connection successful")
            
            # Connect to Atlas
            print(f"📍 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            
            # Test Atlas connection
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connection successful")
            
            return True
            
        except ConnectionFailure as e:
            print(f"❌ Database connection failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error connecting to databases: {e}")
            return False

    def discover_collections(self):
        """Discover all collections in the local database."""
        try:
            local_collections = self.local_db.list_collection_names()
            print(f"📊 Found {len(local_collections)} collections in local database:")
            
            for collection in local_collections:
                count = self.local_db[collection].count_documents({})
                print(f"   • {collection}: {count} documents")
            
            # Update collections list to include discovered collections
            discovered_collections = []
            for collection in self.collections_to_migrate:
                if collection in local_collections:
                    discovered_collections.append(collection)
            
            # Add any additional collections not in the predefined list
            for collection in local_collections:
                if collection not in discovered_collections and not collection.startswith('system.'):
                    discovered_collections.append(collection)
            
            self.collections_to_migrate = discovered_collections
            print(f"📋 Will migrate {len(self.collections_to_migrate)} collections")
            
            return True
            
        except Exception as e:
            print(f"❌ Error discovering collections: {e}")
            return False

    def migrate_collection(self, collection_name):
        """Migrate a single collection from local to Atlas."""
        try:
            print(f"\n📦 Migrating collection: {collection_name}")
            print("=" * 50)
            
            local_collection = self.local_db[collection_name]
            atlas_collection = self.atlas_db[collection_name]
            
            # Get total document count
            total_docs = local_collection.count_documents({})
            self.stats[collection_name]['total'] = total_docs
            
            if total_docs == 0:
                print(f"⚠️  Collection {collection_name} is empty, skipping...")
                return True
            
            print(f"📊 Total documents to migrate: {total_docs}")
            
            # Create indexes if they exist in source
            self.copy_indexes(local_collection, atlas_collection)
            
            # Migrate documents in batches
            batch_size = 100
            migrated_count = 0
            
            cursor = local_collection.find({})
            batch = []
            
            for doc in cursor:
                batch.append(doc)
                
                if len(batch) >= batch_size:
                    migrated_count += self.migrate_batch(atlas_collection, batch, collection_name)
                    batch = []
                    
                    # Progress update
                    progress = (migrated_count / total_docs) * 100
                    print(f"📈 Progress: {migrated_count}/{total_docs} ({progress:.1f}%)")
            
            # Migrate remaining documents
            if batch:
                migrated_count += self.migrate_batch(atlas_collection, batch, collection_name)
            
            print(f"✅ Collection {collection_name} migration completed")
            print(f"📊 Migrated: {self.stats[collection_name]['migrated']}")
            print(f"📊 Skipped: {self.stats[collection_name]['skipped']}")
            print(f"📊 Errors: {self.stats[collection_name]['errors']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error migrating collection {collection_name}: {e}")
            traceback.print_exc()
            return False

    def migrate_batch(self, atlas_collection, batch, collection_name):
        """Migrate a batch of documents with conflict handling."""
        migrated_in_batch = 0
        
        for doc in batch:
            try:
                # Check if document already exists
                existing_doc = atlas_collection.find_one({'_id': doc['_id']})
                
                if existing_doc:
                    # Handle conflicts based on collection type
                    if self.should_update_existing(collection_name, doc, existing_doc):
                        atlas_collection.replace_one({'_id': doc['_id']}, doc)
                        self.stats[collection_name]['migrated'] += 1
                        migrated_in_batch += 1
                    else:
                        self.stats[collection_name]['skipped'] += 1
                else:
                    # Insert new document
                    atlas_collection.insert_one(doc)
                    self.stats[collection_name]['migrated'] += 1
                    migrated_in_batch += 1
                    
            except DuplicateKeyError:
                # Handle unique constraint violations
                if collection_name == 'users':
                    print(f"⚠️  Duplicate user email found, skipping: {doc.get('email', 'unknown')}")
                self.stats[collection_name]['skipped'] += 1
                
            except Exception as e:
                print(f"❌ Error migrating document {doc.get('_id', 'unknown')}: {e}")
                self.stats[collection_name]['errors'] += 1
        
        return migrated_in_batch

    def should_update_existing(self, collection_name, new_doc, existing_doc):
        """Determine if an existing document should be updated."""
        # For users, update if the new document has more recent data
        if collection_name == 'users':
            new_updated = new_doc.get('updated_at')
            existing_updated = existing_doc.get('updated_at')
            
            if new_updated and existing_updated:
                return new_updated > existing_updated
            elif new_updated:
                return True
        
        # For other collections, update if new document has more fields or newer timestamp
        new_fields = len(new_doc.keys())
        existing_fields = len(existing_doc.keys())
        
        return new_fields > existing_fields

    def copy_indexes(self, source_collection, target_collection):
        """Copy indexes from source to target collection."""
        try:
            indexes = source_collection.list_indexes()
            for index in indexes:
                if index['name'] != '_id_':  # Skip default _id index
                    try:
                        # Extract index specification
                        key_spec = index['key']
                        options = {k: v for k, v in index.items() 
                                 if k not in ['key', 'v', 'ns']}
                        
                        target_collection.create_index(list(key_spec.items()), **options)
                        print(f"📋 Created index: {index['name']}")
                    except Exception as e:
                        print(f"⚠️  Could not create index {index['name']}: {e}")
        except Exception as e:
            print(f"⚠️  Could not copy indexes: {e}")

    def verify_migration(self):
        """Verify that the migration was successful by comparing data."""
        print("\n🔍 VERIFYING MIGRATION")
        print("=" * 50)

        verification_passed = True

        for collection_name in self.collections_to_migrate:
            try:
                local_count = self.local_db[collection_name].count_documents({})
                atlas_count = self.atlas_db[collection_name].count_documents({})

                print(f"📊 {collection_name}:")
                print(f"   Local: {local_count} documents")
                print(f"   Atlas: {atlas_count} documents")

                if atlas_count < local_count:
                    missing = local_count - atlas_count
                    print(f"   ⚠️  {missing} documents may be missing or skipped")
                elif atlas_count >= local_count:
                    print(f"   ✅ Migration appears successful")

                # Spot check: verify a few random documents
                if local_count > 0:
                    self.spot_check_collection(collection_name)

            except Exception as e:
                print(f"❌ Error verifying {collection_name}: {e}")
                verification_passed = False

        return verification_passed

    def spot_check_collection(self, collection_name):
        """Perform spot checks on migrated data."""
        try:
            # Get a few random documents from local
            local_docs = list(self.local_db[collection_name].aggregate([
                {'$sample': {'size': min(3, self.local_db[collection_name].count_documents({}))}}
            ]))

            for doc in local_docs:
                atlas_doc = self.atlas_db[collection_name].find_one({'_id': doc['_id']})
                if atlas_doc:
                    # Compare key fields
                    if collection_name == 'users':
                        if doc.get('email') == atlas_doc.get('email'):
                            print(f"   ✅ User {doc.get('email', 'unknown')} verified")
                        else:
                            print(f"   ❌ User data mismatch for {doc.get('_id')}")
                    else:
                        print(f"   ✅ Document {doc['_id']} found in Atlas")
                else:
                    print(f"   ❌ Document {doc['_id']} missing in Atlas")

        except Exception as e:
            print(f"   ⚠️  Spot check failed: {e}")

    def print_migration_summary(self):
        """Print a comprehensive summary of the migration."""
        print("\n📋 MIGRATION SUMMARY")
        print("=" * 50)

        total_migrated = 0
        total_skipped = 0
        total_errors = 0

        for collection_name, stats in self.stats.items():
            if stats['total'] > 0:
                print(f"\n📦 {collection_name}:")
                print(f"   Total: {stats['total']}")
                print(f"   Migrated: {stats['migrated']}")
                print(f"   Skipped: {stats['skipped']}")
                print(f"   Errors: {stats['errors']}")

                total_migrated += stats['migrated']
                total_skipped += stats['skipped']
                total_errors += stats['errors']

        print(f"\n🎯 OVERALL TOTALS:")
        print(f"   Migrated: {total_migrated}")
        print(f"   Skipped: {total_skipped}")
        print(f"   Errors: {total_errors}")

        if total_errors == 0:
            print("✅ Migration completed successfully!")
        else:
            print(f"⚠️  Migration completed with {total_errors} errors")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.local_client:
                self.local_client.close()
                print("🔌 Local MongoDB connection closed")

            if self.atlas_client:
                self.atlas_client.close()
                print("🔌 Atlas MongoDB connection closed")

        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_migration(self):
        """Execute the complete migration process."""
        print("🚀 MONGODB MIGRATION: LOCAL TO ATLAS")
        print("=" * 50)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # Step 1: Load configuration
            if not self.load_atlas_config():
                return False

            # Step 2: Connect to databases
            if not self.connect_databases():
                return False

            # Step 3: Discover collections
            if not self.discover_collections():
                return False

            # Step 4: Migrate each collection
            migration_success = True
            for collection_name in self.collections_to_migrate:
                if not self.migrate_collection(collection_name):
                    migration_success = False

            # Step 5: Verify migration
            self.verify_migration()

            # Step 6: Print summary
            self.print_migration_summary()

            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            return migration_success

        except KeyboardInterrupt:
            print("\n⚠️  Migration interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Migration failed with error: {e}")
            traceback.print_exc()
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run the migration."""
    print("MongoDB Data Migration Tool")
    print("Local MongoDB → MongoDB Atlas")
    print("-" * 40)

    # Confirm before proceeding
    response = input("\n⚠️  This will migrate data from local MongoDB to Atlas. Continue? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("Migration cancelled.")
        return

    # Run migration
    migrator = MongoMigrator()
    success = migrator.run_migration()

    if success:
        print("\n🎉 Migration completed successfully!")
        print("Your SisaRasa application should now work with MongoDB Atlas.")
    else:
        print("\n❌ Migration failed or completed with errors.")
        print("Please check the logs above and retry if necessary.")


if __name__ == "__main__":
    main()
