#!/usr/bin/env python3
"""
MongoDB Migration Verification Script
=====================================

This script provides detailed verification of the data migration from local MongoDB to Atlas.
It performs comprehensive checks to ensure data integrity and completeness.
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class MigrationVerifier:
    def __init__(self):
        """Initialize the migration verifier."""
        self.local_uri = "mongodb://localhost:27017/"
        self.local_db_name = "sisarasa"
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        
        self.local_client = None
        self.atlas_client = None
        self.local_db = None
        self.atlas_db = None
        
        self.verification_results = {}

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_databases(self):
        """Connect to both databases."""
        try:
            print("🔗 Connecting to databases...")
            
            # Connect to local MongoDB
            self.local_client = MongoClient(self.local_uri, serverSelectionTimeoutMS=5000)
            self.local_db = self.local_client[self.local_db_name]
            self.local_client.admin.command('ping')
            print("✅ Local MongoDB connected")
            
            # Connect to Atlas
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def verify_collection_counts(self):
        """Verify document counts for all collections."""
        print("\n📊 COLLECTION COUNT VERIFICATION")
        print("=" * 50)
        
        local_collections = self.local_db.list_collection_names()
        atlas_collections = self.atlas_db.list_collection_names()
        
        all_collections = set(local_collections + atlas_collections)
        
        for collection in sorted(all_collections):
            local_count = self.local_db[collection].count_documents({}) if collection in local_collections else 0
            atlas_count = self.atlas_db[collection].count_documents({}) if collection in atlas_collections else 0
            
            status = "✅" if atlas_count >= local_count else "❌" if atlas_count < local_count else "⚠️"
            
            print(f"{status} {collection}:")
            print(f"   Local: {local_count:,} documents")
            print(f"   Atlas: {atlas_count:,} documents")
            
            if atlas_count < local_count:
                missing = local_count - atlas_count
                print(f"   Missing: {missing:,} documents")
            elif atlas_count > local_count:
                extra = atlas_count - local_count
                print(f"   Extra: {extra:,} documents (may include new data)")
            
            self.verification_results[collection] = {
                'local_count': local_count,
                'atlas_count': atlas_count,
                'status': 'pass' if atlas_count >= local_count else 'fail'
            }

    def verify_user_data_integrity(self):
        """Verify user data integrity and relationships."""
        print("\n👤 USER DATA INTEGRITY VERIFICATION")
        print("=" * 50)
        
        try:
            # Check if users collection exists
            if 'users' not in self.local_db.list_collection_names():
                print("⚠️  No users collection found in local database")
                return
            
            local_users = list(self.local_db.users.find({}))
            print(f"📊 Checking {len(local_users)} users...")
            
            verified_users = 0
            missing_users = 0
            data_mismatches = 0
            
            for user in local_users:
                atlas_user = self.atlas_db.users.find_one({'_id': user['_id']})
                
                if not atlas_user:
                    missing_users += 1
                    print(f"❌ Missing user: {user.get('email', 'unknown')} (ID: {user['_id']})")
                    continue
                
                # Verify key fields
                email_match = user.get('email') == atlas_user.get('email')
                name_match = user.get('name') == atlas_user.get('name')
                
                if email_match and name_match:
                    verified_users += 1
                    
                    # Check saved recipes
                    local_saved = set(user.get('saved_recipes', []))
                    atlas_saved = set(atlas_user.get('saved_recipes', []))
                    
                    if local_saved != atlas_saved:
                        missing_recipes = local_saved - atlas_saved
                        if missing_recipes:
                            print(f"⚠️  User {user['email']} missing {len(missing_recipes)} saved recipes")
                else:
                    data_mismatches += 1
                    print(f"❌ Data mismatch for user: {user.get('email', 'unknown')}")
            
            print(f"\n📊 User Verification Results:")
            print(f"   ✅ Verified: {verified_users}")
            print(f"   ❌ Missing: {missing_users}")
            print(f"   ⚠️  Mismatches: {data_mismatches}")
            
        except Exception as e:
            print(f"❌ Error verifying user data: {e}")

    def verify_data_relationships(self):
        """Verify relationships between collections."""
        print("\n🔗 DATA RELATIONSHIP VERIFICATION")
        print("=" * 50)
        
        try:
            # Verify user-recipe relationships
            if 'users' in self.atlas_db.list_collection_names():
                users_with_saved_recipes = list(self.atlas_db.users.find(
                    {'saved_recipes': {'$exists': True, '$ne': []}}
                ))
                
                print(f"👤 Found {len(users_with_saved_recipes)} users with saved recipes")
                
                orphaned_references = 0
                for user in users_with_saved_recipes[:10]:  # Check first 10 users
                    saved_recipes = user.get('saved_recipes', [])
                    for recipe_id in saved_recipes:
                        # Check if this is a reference to recipes collection or external ID
                        if isinstance(recipe_id, ObjectId):
                            recipe_exists = self.atlas_db.recipes.find_one({'_id': recipe_id})
                            if not recipe_exists:
                                orphaned_references += 1
                
                if orphaned_references > 0:
                    print(f"⚠️  Found {orphaned_references} orphaned recipe references")
                else:
                    print("✅ Recipe references appear valid")
            
            # Verify review-user relationships
            if 'reviews' in self.atlas_db.list_collection_names():
                reviews = list(self.atlas_db.reviews.find({}).limit(10))
                orphaned_reviews = 0
                
                for review in reviews:
                    user_id = review.get('user_id')
                    if user_id:
                        user_exists = self.atlas_db.users.find_one({'_id': user_id})
                        if not user_exists:
                            orphaned_reviews += 1
                
                if orphaned_reviews > 0:
                    print(f"⚠️  Found {orphaned_reviews} reviews with missing users")
                else:
                    print("✅ Review-user relationships appear valid")
                    
        except Exception as e:
            print(f"❌ Error verifying relationships: {e}")

    def verify_indexes(self):
        """Verify that indexes were copied correctly."""
        print("\n📋 INDEX VERIFICATION")
        print("=" * 50)
        
        try:
            collections_to_check = ['users', 'reviews', 'community_posts']
            
            for collection_name in collections_to_check:
                if collection_name in self.local_db.list_collection_names():
                    local_indexes = list(self.local_db[collection_name].list_indexes())
                    atlas_indexes = list(self.atlas_db[collection_name].list_indexes())
                    
                    local_index_names = {idx['name'] for idx in local_indexes}
                    atlas_index_names = {idx['name'] for idx in atlas_indexes}
                    
                    missing_indexes = local_index_names - atlas_index_names
                    
                    print(f"📦 {collection_name}:")
                    print(f"   Local indexes: {len(local_indexes)}")
                    print(f"   Atlas indexes: {len(atlas_indexes)}")
                    
                    if missing_indexes:
                        print(f"   ❌ Missing indexes: {', '.join(missing_indexes)}")
                    else:
                        print(f"   ✅ All indexes present")
                        
        except Exception as e:
            print(f"❌ Error verifying indexes: {e}")

    def generate_verification_report(self):
        """Generate a comprehensive verification report."""
        print("\n📋 VERIFICATION REPORT")
        print("=" * 50)
        
        total_collections = len(self.verification_results)
        passed_collections = sum(1 for result in self.verification_results.values() 
                               if result['status'] == 'pass')
        
        print(f"📊 Collections verified: {total_collections}")
        print(f"✅ Passed verification: {passed_collections}")
        print(f"❌ Failed verification: {total_collections - passed_collections}")
        
        if passed_collections == total_collections:
            print("\n🎉 ALL VERIFICATIONS PASSED!")
            print("Your migration appears to be successful.")
        else:
            print(f"\n⚠️  {total_collections - passed_collections} collections need attention.")
            print("Please review the failed verifications above.")
        
        # Save detailed report to file
        report_file = f"migration_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'verification_results': self.verification_results,
                'summary': {
                    'total_collections': total_collections,
                    'passed': passed_collections,
                    'failed': total_collections - passed_collections
                }
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_file}")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.local_client:
                self.local_client.close()
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_verification(self):
        """Run the complete verification process."""
        print("🔍 MONGODB MIGRATION VERIFICATION")
        print("=" * 50)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_databases():
                return False
            
            self.verify_collection_counts()
            self.verify_user_data_integrity()
            self.verify_data_relationships()
            self.verify_indexes()
            self.generate_verification_report()
            
            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return True
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run verification."""
    print("MongoDB Migration Verification Tool")
    print("-" * 40)
    
    verifier = MigrationVerifier()
    success = verifier.run_verification()
    
    if success:
        print("\n✅ Verification completed!")
    else:
        print("\n❌ Verification failed!")


if __name__ == "__main__":
    main()
